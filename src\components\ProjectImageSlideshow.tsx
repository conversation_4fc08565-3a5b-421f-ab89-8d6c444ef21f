import React, { useEffect, useState, useCallback, useRef } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { type CarouselApi } from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";

interface ProjectImageSlideshowProps {
  images: string[];
  alt: string;
  className?: string;
}

export const ProjectImageSlideshow: React.FC<ProjectImageSlideshowProps> = ({
  images,
  alt,
  className = "",
}) => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [imagesLoaded, setImagesLoaded] = useState<boolean[]>([]);
  const [isReady, setIsReady] = useState(false);
  const autoplayRef = useRef<any>(null);

  // Initialize autoplay plugin
  const plugin = React.useMemo(
    () => Autoplay({ delay: 3000, stopOnInteraction: true }),
    []
  );

  // Store the plugin reference
  useEffect(() => {
    autoplayRef.current = plugin;
  }, [plugin]);

  // Initialize images loaded state
  useEffect(() => {
    if (images && images.length > 0) {
      setImagesLoaded(new Array(images.length).fill(false));
      setCurrent(0);
      setIsReady(false);
    }
  }, [images]);

  // Handle image load
  const handleImageLoad = useCallback((index: number) => {
    setImagesLoaded((prev) => {
      const newState = [...prev];
      newState[index] = true;
      return newState;
    });
  }, []);

  // Check if all images are loaded
  useEffect(() => {
    if (images && images.length > 0 && imagesLoaded.length === images.length) {
      const allLoaded = imagesLoaded.every((loaded) => loaded);
      if (allLoaded && !isReady) {
        setIsReady(true);
      }
    }
  }, [imagesLoaded, images, isReady]);

  // Setup carousel API listeners
  useEffect(() => {
    if (!api || !images || images.length === 0) {
      return;
    }

    const updateCurrent = () => {
      const selectedIndex = api.selectedScrollSnap();
      if (selectedIndex >= 0 && selectedIndex < images.length) {
        setCurrent(selectedIndex);
      }
    };

    // Set initial current index
    updateCurrent();

    // Listen for slide changes
    api.on("select", updateCurrent);

    return () => {
      api.off("select", updateCurrent);
    };
  }, [api, images]);

  // Reset autoplay when images change
  useEffect(() => {
    if (autoplayRef.current && isReady) {
      autoplayRef.current.reset();
    }
  }, [isReady]);

  // If no images provided, show a placeholder
  if (!images || images.length === 0) {
    return (
      <div
        className={`w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-lg flex items-center justify-center ${className}`}
      >
        <div className="text-4xl">📷</div>
      </div>
    );
  }

  // If only one image, show it without carousel
  if (images.length === 1) {
    return (
      <div className={`w-full h-full rounded-lg overflow-hidden ${className}`}>
        <img
          src={images[0]}
          alt={alt}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.style.display = "none";
            target.parentElement!.innerHTML =
              '<div class="w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-lg flex items-center justify-center"><div class="text-4xl">📷</div></div>';
          }}
        />
      </div>
    );
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      <Carousel
        setApi={setApi}
        className="w-full h-full"
        plugins={[plugin]}
        opts={{
          align: "start",
          loop: true,
        }}
        onMouseEnter={() => autoplayRef.current?.stop()}
        onMouseLeave={() => autoplayRef.current?.reset()}
      >
        <CarouselContent className="h-full">
          {images.map((image, index) => (
            <CarouselItem key={`${image}-${index}`} className="h-full">
              <div className="w-full h-full rounded-lg overflow-hidden">
                <img
                  src={image}
                  alt={`${alt} - Image ${index + 1}`}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  onLoad={() => handleImageLoad(index)}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = "none";
                    target.parentElement!.innerHTML =
                      '<div class="w-full h-full bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-lg flex items-center justify-center"><div class="text-4xl">📷</div></div>';
                    // Mark as loaded even on error to prevent hanging
                    handleImageLoad(index);
                  }}
                />
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>

      {/* Loading indicator */}
      {!isReady && images.length > 1 && (
        <div className="absolute inset-0 bg-slate-900/50 rounded-lg flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {/* Dots indicator */}
      {images.length > 1 && isReady && (
        <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
          {images.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                index === current
                  ? "bg-cyan-400 scale-110"
                  : "bg-white/50 hover:bg-white/70"
              }`}
              onClick={() => {
                if (api && index >= 0 && index < images.length) {
                  api.scrollTo(index);
                }
              }}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
