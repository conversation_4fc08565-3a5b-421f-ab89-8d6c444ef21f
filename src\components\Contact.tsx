import { Mail, MessageSquare, Gith<PERSON>, Linkedin, Instagram } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const Contact = () => {
  const testimonials = [
    {
      text: "<PERSON><PERSON><PERSON> has demonstrated exceptional problem-solving skills by solving 400+ problems on LeetCode. His approach to algorithmic thinking and data structures is commendable.",
      author: "LeetCode Community",
      role: "Competitive Programming Platform",
      avatar: "🏆",
      gradient: "from-purple-500 to-pink-500",
    },
    {
      text: "Outstanding performance in the Full-Stack Web Development Bootcamp. Tanishq showed great dedication to learning modern web technologies and best practices.",
      author: "Udemy Instructor",
      role: "Full-Stack Development Course",
      avatar: "🎯",
      gradient: "from-cyan-500 to-blue-500",
    },
    {
      text: "Impressive blockchain project showcasing deep understanding of decentralized systems and smart contract development. Great attention to security and user experience.",
      author: "Tech Reviewer",
      role: "Blockchain Technology Expert",
      avatar: "⛓️",
      gradient: "from-purple-600 to-indigo-500",
    },
  ];

  return (
    <section id="contact" className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Info */}
          <div>
            <h2 className="text-4xl lg:text-5xl font-bold mb-8 bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
              Let's Connect & Build Something Amazing
            </h2>

            <div className="space-y-6 mb-12">
              <p className="text-lg text-slate-300 leading-relaxed">
                I'm actively seeking opportunities to collaborate on innovative
                projects and contribute to meaningful solutions.
              </p>
              <p className="text-lg text-slate-300 leading-relaxed">
                Whether it's full-stack development, blockchain solutions, or
                AI/ML projects, I'm excited to discuss how we can work together!
              </p>
            </div>

            <div className="space-y-4 mb-12">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-3 text-cyan-400 hover:text-cyan-300 transition-colors cursor-pointer"
              >
                <Mail size={20} />
                <span className="text-lg underline"><EMAIL></span>
              </a>

              <div className="space-y-2 text-slate-300">
                <div className="flex items-center space-x-3">
                  <MessageSquare size={20} className="text-slate-400" />
                  <span>Available for freelance opportunities</span>
                </div>
                <a
                  href="https://www.linkedin.com/in/tanishqbhartwal777"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 hover:text-cyan-400 transition-colors cursor-pointer"
                >
                  <Linkedin size={20} className="text-slate-400" />
                  <span>Open to professional networking</span>
                </a>
                <a
                  href="https://www.instagram.com/tanishqb_7?igsh=ZHN6Mzk5aDZ0enBz"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 hover:text-cyan-400 transition-colors cursor-pointer"
                >
                  <Instagram size={20} className="text-slate-400" />
                  <span>Follow my tech journey</span>
                </a>
                <a
                  href="https://github.com/RsTanishq"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-3 hover:text-cyan-400 transition-colors cursor-pointer"
                >
                  <Github size={20} className="text-slate-400" />
                  <span>Check out my repositories</span>
                </a>
              </div>
            </div>

            {/* Status indicator */}
            <div className="flex items-center space-x-2 text-slate-400">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse animation-delay-200"></div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse animation-delay-400"></div>
              <span className="ml-2 text-green-400">
                Available for new opportunities
              </span>
            </div>
          </div>

          {/* Testimonials */}
          <div className="space-y-6">
            {testimonials.map((testimonial, index) => (
              <Card
                key={index}
                className={`bg-gradient-to-br ${testimonial.gradient} p-6 text-white relative overflow-hidden`}
              >
                <div className="absolute top-4 left-4 text-4xl opacity-30">
                  "
                </div>
                <div className="absolute top-4 right-4 text-4xl opacity-30">
                  "
                </div>

                <div className="relative z-10">
                  <div className="flex items-start space-x-4 mb-4">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl">
                      {testimonial.avatar}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm leading-relaxed mb-4">
                        {testimonial.text}
                      </p>
                      <div>
                        <div className="font-semibold">
                          {testimonial.author}
                        </div>
                        <div className="text-sm opacity-90">
                          {testimonial.role}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
