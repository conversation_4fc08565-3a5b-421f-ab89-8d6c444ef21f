
import { ExternalLink, Github } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const Work = () => {
  const projects = [
    {
      title: "Weather Web App",
      category: "Web Development",
      description: "A responsive weather web app created using HTML, CSS, JavaScript, and Tailwind CSS. Delivers real-time weather updates, 7-day forecasts, and global search functionality.",
      image: "/lovable-uploads/placeholder.svg",
      tags: ["HTML", "CSS", "JavaScript", "Tailwind CSS"],
      date: "Jan 2024"
    },
    {
      title: "Blockchain Healthcare Management",
      category: "Blockchain Development", 
      description: "A decentralized healthcare management system leveraging blockchain technology for secure and transparent record handling with smart contracts.",
      image: "/lovable-uploads/placeholder.svg",
      tags: ["MERN Stack", "Ethereum", "Smart Contracts", "React.js"],
      date: "Feb 2025",
      featured: true
    }
  ];

  const categories = ["All", "Web Development", "Blockchain Development", "AI/ML"];

  return (
    <section id="work" className="py-20 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent">
            My Projects
          </h2>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
            Developed innovative solutions ranging from weather applications to blockchain-based healthcare systems. 
            Specialized in full-stack development with modern web technologies and smart contracts.
          </p>
        </div>

        {/* Filter Buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category, index) => (
            <Button
              key={category}
              variant="outline"
              className={`border-slate-600 text-slate-300 hover:bg-cyan-500/20 hover:border-cyan-400 hover:text-cyan-400 ${
                index === 0 ? "bg-cyan-500/20 border-cyan-400 text-cyan-400" : ""
              }`}
            >
              <span className="text-cyan-400 mr-1 text-xs">0{index + 1}/</span>
              {category}
            </Button>
          ))}
        </div>

        {/* Featured Project */}
        <div className="mb-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-white mb-4">Featured Project</h3>
              <h4 className="text-2xl font-semibold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-4">
                Blockchain Healthcare Management System
              </h4>
              <p className="text-slate-300 mb-6 leading-relaxed">
                Developed a decentralized healthcare management system leveraging blockchain technology for secure and transparent record handling. 
                Implemented core features such as patient records, doctor prescriptions, appointment tracking, and payment management (both crypto and cash-based).
                Designed a file-management-style interface using React.js (Vite) for an intuitive admin dashboard and user experience.
              </p>
              <div className="flex flex-wrap gap-2 mb-6">
                {["MERN Stack", "Ethereum", "Smart Contracts", "React.js"].map((tag) => (
                  <span key={tag} className="px-3 py-1 bg-slate-700 text-cyan-400 rounded-full text-sm">
                    {tag}
                  </span>
                ))}
              </div>
              <Button className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white">
                View Project
              </Button>
            </div>
            
            <div className="relative">
              <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 border border-slate-700">
                <div className="w-full h-64 bg-gradient-to-br from-cyan-500/20 to-purple-500/20 rounded-lg flex items-center justify-center">
                  <div className="text-6xl">⛓️</div>
                </div>
              </div>
              {/* Decorative Arrow */}
              <div className="absolute -top-4 -right-4 w-16 h-16">
                <div className="w-full h-full border-2 border-purple-500 rounded-full flex items-center justify-center">
                  <ExternalLink size={20} className="text-purple-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Project Grid */}
        <div className="grid md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <Card key={index} className="bg-gradient-to-br from-slate-800 to-slate-900 border-slate-700 overflow-hidden hover:scale-105 transition-transform duration-300">
              <div className="h-48 bg-gradient-to-br from-cyan-500/20 to-purple-500/20 flex items-center justify-center">
                <div className="text-4xl">{project.category === "Blockchain Development" ? "⛓️" : "🌤️"}</div>
              </div>
              <div className="p-6">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-cyan-400 text-sm">{project.category}</div>
                  <div className="text-slate-500 text-sm">{project.date}</div>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">{project.title}</h3>
                <p className="text-slate-400 text-sm mb-4">{project.description}</p>
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.tags.map((tag) => (
                    <span key={tag} className="px-2 py-1 bg-slate-700 text-slate-300 rounded text-xs">
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="flex justify-between items-center">
                  <div className="flex space-x-2">
                    <Button variant="ghost" size="icon" className="text-slate-400 hover:text-cyan-400">
                      <Github size={16} />
                    </Button>
                    <Button variant="ghost" size="icon" className="text-slate-400 hover:text-cyan-400">
                      <ExternalLink size={16} />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Work;
